{% extends "fe/include/base.html" %}

{% set seoTitle = '' %}
{% set seoDescription = '' %}

{% block title %}{{ seoTitle }}{% endblock %}

{% block canonical %}
<meta name="robots" content="index, follow">
<meta name="description" content="{{ seoDescription }}">    
<link rel="canonical" href="{{ publicUrl }}">
{% endblock %}

{% block socialcards %}
<meta property="og:locale"          content="it_IT" />
<meta property="og:url"             content="{{ publicUrl }}" />
<meta property="og:type"            content="website" />
<meta property="og:title"           content="{{ seoTitle }}" />
<meta property="og:description"     content="{{ seoDescription }}" />
<meta property="og:image"           content="" />
<meta property="og:image:width"     content="1200" />
<meta property="og:image:height"    content="630" />
<meta property="og:image:alt"       content="{{ seoTitle }}" />
{% endblock %}

{% block content %}

    <!-- Page content -->
    <main class="content-wrapper">
        <div class="container pt-4 pb-5 mb-xxl-3">

            <!-- Breadcrumb -->
            <nav class="pb-2 pb-md-3" aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ routes }}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Ricerca</li>
                </ol>
            </nav>


            <!-- Filter sidebar + Listings grid view -->
            <div class="row pb-2 pb-sm-3 pb-md-4 pb-lg-5">

                <!-- Filter sidebar that turns into offcanvas on screens < 992px wide (lg breakpoint) -->
                <aside class="col-lg-3">
                    <div class="offcanvas-lg offcanvas-start pe-lg-2 pe-xl-3 pe-xxl-4" id="filterSidebar">
                        <div class="offcanvas-header border-bottom py-3">
                            <h3 class="h5 offcanvas-title">Filtri</h3>
                            <button type="button" class="btn-close d-lg-none" data-bs-dismiss="offcanvas" data-bs-target="#filterSidebar" aria-label="Chiudi"></button>
                        </div>
                        <div class="offcanvas-body d-block">

                            <!-- Active filters + Search -->
                            <div class="mb-2 mb-xl-3">
                                <div class="d-flex align-items-center justify-content-between mb-3">
                                    <h4 class="h6 mb-0">Filtri</h4>
                                    <div class="nav">
                                        <a class="nav-link fs-xs text-decoration-underline text-nowrap p-0" href="#!">Togli filtri</a>
                                    </div>
                                </div>
                                <div class="d-flex flex-wrap gap-2 mb-4">
                                    <button type="button" class="btn btn-sm btn-secondary rounded-pill">
                                        <i class="fi-close fs-sm me-1 ms-n1"></i>
                                        Reef
                                    </button>
                                    <button type="button" class="btn btn-sm btn-secondary rounded-pill">
                                        <i class="fi-close fs-sm me-1 ms-n1"></i>
                                        Gommoni
                                    </button>                                    
                                    <button type="button" class="btn btn-sm btn-secondary rounded-pill">
                                        <i class="fi-close fs-sm me-1 ms-n1"></i>
                                        5
                                        <i class="fi-star-filled text-warning ms-1"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-secondary rounded-pill">
                                        <i class="fi-close fs-sm me-1 ms-n1"></i>
                                        4
                                        <i class="fi-star-filled text-warning ms-1"></i>
                                    </button>
                                </div>
                                <hr>
                            </div>

                            <!-- Luogo -->
                            <div class="pb-4 mb-2 mb-xl-3">
                                <h4 class="h6">Luogo</h4>
                                <select class="form-select form-select-sm" data-select="{
                                        &quot;classNames&quot;: {
                                        &quot;containerInner&quot;: [&quot;form-select&quot;, &quot;form-select-lg&quot;]
                                        },
                                        &quot;searchEnabled&quot;: true
                                        }" aria-label="Seleziona città">
                                    <option value="">Seleziona città</option>

                                </select>
                            </div>


                            <!-- Immersioni -->
                            <div class="pb-4 mb-2 mb-xl-3">
                                <h4 class="h6"><img src="{{ contextPath }}/fe/img/account/diving.svg" alt="Image" width="32" class="me-2"> Immersioni</h4>                                
                                    
                                <!-- Certificazioni accettate -->
                                <div class="position-relative mb-3">
                                    <label for="divingCertifications" class="form-label">Certificazioni accettate</label>
                                    <!-- Multiple select example -->
                                    <select class="form-select" id="divingCertifications" name="divingCertifications" data-select multiple aria-label="Certificazioni accettate">
                                        <option value="">Seleziona certificazioni</option>

                                        <optgroup label="Immersioni Ricreative">
                                            <option value="padi">PADI</option>
                                            <option value="ssi">SSI</option>
                                            <option value="cmas">CMAS</option>
                                            <option value="fipsas">FIPSAS</option>
                                            <option value="anis">ANIS</option>
                                            <option value="naui">NAUI</option>
                                            <option value="bsac">BSAC</option>
                                            <option value="fias">FIAS</option>
                                        </optgroup>

                                        <optgroup label="Immersioni Tecniche">
                                            <option value="tdi">TDI</option>
                                            <option value="gue">GUE</option>
                                            <option value="iantd">IANTD</option>
                                            <option value="andi">ANDI</option>
                                            <option value="padi-tec">PADI Tec</option>
                                            <option value="ssi-tec">SSI Extended Range</option>
                                            <option value="raid-tec">RAID Technical</option>
                                            <option value="psai">PSAI</option>
                                        </optgroup>

                                        <optgroup label="Altro">
                                            <option value="other">Altre certificazioni</option>
                                        </optgroup>
                                    </select>
                                </div>                                                                                                        
                                
                                <!-- Tipologia immersioni -->
                                <div data-filter-list="{&quot;searchClass&quot;: &quot;dive-search&quot;, &quot;listClass&quot;: &quot;dive-list&quot;, &quot;valueNames&quot;: [&quot;form-check-label&quot;]}">
                                    <div class="position-relative mb-3">
                                        <label class="form-label">Tipologie di immersioni</label>
                                        <div class="position-relative">
                                            <i class="fi-search position-absolute top-50 start-0 translate-middle-y ms-3"></i>
                                            <input type="search" class="dive-search form-control form-icon-start" placeholder="Cerca tipologie">
                                        </div>
                                    </div>
                                    <div class="position-relative mb-4" style="height: 215px" data-simplebar="" data-simplebar-auto-hide="false">
                                        <div class="dive-list d-flex flex-column gap-2">
                                            <div class="form-check mb-0">
                                                <input type="checkbox" class="form-check-input" id="divingReef" name="divingReef">
                                                <label class="form-check-label" for="divingReef">
                                                    <img src="{{ contextPath }}/fe/img/icons/reef.svg" alt="Image" class="w-25px">
                                                    Reef
                                                </label>
                                            </div>
                                            <div class="form-check mb-0">
                                                <input type="checkbox" class="form-check-input" id="divingWrecks" name="divingWrecks">
                                                <label for="divingWrecks" class="form-check-label">
                                                    <img src="{{ contextPath }}/fe/img/icons/wrecks.svg" alt="Image" class="w-25px">
                                                    Relitti
                                                </label>
                                            </div>
                                            <div class="form-check mb-0">
                                                <input type="checkbox" class="form-check-input" id="divingNightDives" name="divingNightDives">
                                                <label for="divingNightDives" class="form-check-label">
                                                    <img src="{{ contextPath }}/fe/img/icons/night-dives.svg" alt="Image" class="w-25px">
                                                    Notturne
                                                </label>
                                            </div>
                                            <div class="form-check mb-0">
                                                <input type="checkbox" class="form-check-input" id="divingSeaBaptism" name="divingSeaBaptism">
                                                <label for="divingSeaBaptism" class="form-check-label">
                                                    <img src="{{ contextPath }}/fe/img/icons/sea-baptism.svg" alt="Image" class="w-25px">
                                                    Battesimi del mare
                                                </label>
                                            </div>
                                            <div class="form-check mb-0">
                                                <input type="checkbox" class="form-check-input" id="divingOpenWater" name="divingOpenWater">
                                                <label for="divingOpenWater" class="form-check-label">
                                                    <img src="{{ contextPath }}/fe/img/icons/open-water.svg" alt="Image" class="w-25px">
                                                    Acque libere
                                                </label>
                                            </div>
                                            <div class="form-check mb-0">
                                                <input type="checkbox" class="form-check-input" id="divingShoreDeparture" name="divingShoreDeparture">
                                                <label for="divingShoreDeparture" class="form-check-label">
                                                    <img src="{{ contextPath }}/fe/img/icons/shore-departure.svg" alt="Image" class="w-25px">
                                                    Partenza da riva
                                                </label>
                                            </div>
                                            <div class="form-check mb-0">
                                                <input type="checkbox" class="form-check-input" id="divingUnderIce" name="divingUnderIce">
                                                <label for="divingUnderIce" class="form-check-label">
                                                    <img src="{{ contextPath }}/fe/img/icons/ice.svg" alt="Image" class="w-25px">
                                                    Acque fredde \ Ghiacci
                                                </label>
                                            </div>
                                            <div class="form-check mb-0">
                                                <input type="checkbox" class="form-check-input" id="divingCurrents" name="divingCurrents">
                                                <label for="divingCurrents" class="form-check-label">
                                                    <img src="{{ contextPath }}/fe/img/icons/currents.svg" alt="Image" class="w-25px">
                                                    In corrente
                                                </label>
                                            </div>
                                            <div class="form-check mb-0">
                                                <input type="checkbox" class="form-check-input" id="divingAltitude" name="divingAltitude">
                                                <label for="divingAltitude" class="form-check-label">
                                                    <img src="{{ contextPath }}/fe/img/icons/altitude.svg" alt="Image" class="w-25px">
                                                    In altitudine
                                                </label>
                                            </div>
                                            <div class="form-check mb-0">
                                                <input type="checkbox" class="form-check-input" id="divingSidemount" name="divingSidemount">
                                                <label for="divingSidemount" class="form-check-label">
                                                    <img src="{{ contextPath }}/fe/img/icons/sidemount.svg" alt="Image" class="w-25px">
                                                    In sidemount
                                                </label>
                                            </div>
                                            <div class="form-check mb-0">
                                                <input type="checkbox" class="form-check-input" id="divingScooter" name="divingScooter">
                                                <label for="divingScooter" class="form-check-label">
                                                    <img src="{{ contextPath }}/fe/img/icons/scooter.svg" alt="Image" class="w-25px">
                                                    Con scooter Subacqueo
                                                </label>
                                            </div>
                                            <div class="form-check mb-0">
                                                <input type="checkbox" class="form-check-input" id="divingSharkCage" name="divingSharkCage">
                                                <label for="divingSharkCage" class="form-check-label">
                                                    <img src="{{ contextPath }}/fe/img/icons/shark.svg" alt="Image" class="w-25px">
                                                    In gabbia (Squalo)
                                                </label>
                                            </div>
                                            <div class="form-check mb-0">
                                                <input type="checkbox" class="form-check-input" id="divingCenotes" name="divingCenotes">
                                                <label for="divingCenotes" class="form-check-label">
                                                    <img src="{{ contextPath }}/fe/img/icons/cenotes.svg" alt="Image" class="w-25px">
                                                    Nei cenotes
                                                </label>
                                            </div>
                                            <div class="form-check mb-0">
                                                <input type="checkbox" class="form-check-input" id="divingBigAnimals" name="divingBigAnimals">
                                                <label for="divingBigAnimals" class="form-check-label">
                                                    <img src="{{ contextPath }}/fe/img/icons/big-animals.svg" alt="Image" class="w-25px">
                                                    Con grandi animali
                                                </label>
                                            </div>
                                            <div class="form-check mb-0">
                                                <input type="checkbox" class="form-check-input" id="divingSardineRun" name="divingSardineRun">
                                                <label for="divingSardineRun" class="form-check-label">
                                                    <img src="{{ contextPath }}/fe/img/icons/sardine-run.svg" alt="Image" class="w-25px">
                                                    Sardine run
                                                </label>
                                            </div>
                                            <div class="form-check mb-0">
                                                <input type="checkbox" class="form-check-input" id="divingPoolY40" name="divingPoolY40">
                                                <label for="divingPoolY40" class="form-check-label">
                                                    <img src="{{ contextPath }}/fe/img/icons/pool-y40.svg" alt="Image" class="w-25px">
                                                    In piscina (Y40)
                                                </label>
                                            </div>
                                            <div class="form-check mb-0">
                                                <input type="checkbox" class="form-check-input" id="divingFreshWater" name="divingFreshWater">
                                                <label for="divingFreshWater" class="form-check-label">
                                                    <img src="{{ contextPath }}/fe/img/icons/fresh-water.svg" alt="Image" class="w-25px">
                                                    In acqua dolce
                                                </label>
                                            </div>
                                            <div class="form-check mb-0">
                                                <input type="checkbox" class="form-check-input" id="divingFocusBio" name="divingFocusBio">
                                                <label for="divingFocusBio" class="form-check-label">
                                                    <img src="{{ contextPath }}/fe/img/icons/focus-bio.svg" alt="Image" class="w-25px">
                                                    Focus biologia
                                                </label>
                                            </div>
                                            <div class="form-check mb-0">
                                                <input type="checkbox" class="form-check-input" id="divingFocusPhoto" name="divingFocusPhoto">
                                                <label for="divingFocusPhoto" class="form-check-label">
                                                    <img src="{{ contextPath }}/fe/img/icons/underwater-photography.svg" alt="Image" class="w-25px">
                                                    Focus Fotografia
                                                </label>
                                            </div>
                                            <div class="form-check mb-0">
                                                <input type="checkbox" class="form-check-input" id="divingFocusVideo" name="divingFocusVideo">
                                                <label for="divingFocusVideo" class="form-check-label">
                                                    <img src="{{ contextPath }}/fe/img/icons/underwater-video.svg" alt="Image" class="w-25px">
                                                    Focus Video
                                                </label>
                                            </div>
                                            <div class="form-check mb-0">
                                                <input type="checkbox" class="form-check-input" id="divingDeepDives" name="divingDeepDives">
                                                <label for="divingDeepDives" class="form-check-label">
                                                    <img src="{{ contextPath }}/fe/img/icons/deep-dives.svg" alt="Image" class="w-25px">
                                                    Immersioni profonde
                                                </label>
                                            </div>
                                            <div class="form-check mb-0">
                                                <input type="checkbox" class="form-check-input" id="divingRebreather" name="divingRebreather">
                                                <label for="divingRebreather" class="form-check-label">
                                                    <img src="{{ contextPath }}/fe/img/icons/rebreather.svg" alt="Image" class="w-25px">
                                                    Rebreather diving
                                                </label>
                                            </div>
                                            <div class="form-check mb-0">
                                                <input type="checkbox" class="form-check-input" id="divingDeco" name="divingDeco">
                                                <label for="divingDeco" class="form-check-label">
                                                    <img src="{{ contextPath }}/fe/img/icons/deco.svg" alt="Image" class="w-25px">
                                                    Deco diving
                                                </label>
                                            </div>
                                            <div class="form-check mb-0">
                                                <input type="checkbox" class="form-check-input" id="divingExtreme" name="divingExtreme">
                                                <label for="divingExtreme" class="form-check-label">
                                                    <img src="{{ contextPath }}/fe/img/icons/extreme.svg" alt="Image" class="w-25px">
                                                    Immersioni estreme
                                                </label>
                                            </div>
                                            <div class="form-check mb-0">
                                                <input type="checkbox" class="form-check-input" id="divingWildlifeTech" name="divingWildlifeTech">
                                                <label for="divingWildlifeTech" class="form-check-label">
                                                    <img src="{{ contextPath }}/fe/img/icons/wildlife.svg" alt="Image" class="w-25px">
                                                    Wildlife
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Foto \ Video -->
                                <div class="position-relative mb-3">
                                    <label class="form-label">Servizi Foto \ Video</label>
                                    <div class="d-flex flex-column gap-2">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="divingUnderwaterPhotography" name="divingUnderwaterPhotography" checked>
                                            <label class="form-check-label fs-sm" for="divingUnderwaterPhotography">
                                                <img src="{{ contextPath }}/fe/img/icons/underwater-photography.svg" alt="Fotografia subacquea" class="ms-n1 me-2 w-25px">
                                                Fotografia subacquea
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="divingUnderwaterVideo" name="divingUnderwaterVideo">
                                            <label class="form-check-label fs-sm" for="divingUnderwaterVideo">
                                                <img src="{{ contextPath }}/fe/img/icons/underwater-video.svg" alt="Video subacqueo" class="ms-n1 me-2 w-25px">
                                                Video subacqueo
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="divingEditingCourse" name="divingEditingCourse">
                                            <label class="form-check-label fs-sm" for="divingEditingCourse">
                                                <img src="{{ contextPath }}/fe/img/icons/editing-course.svg" alt="Corso editing e post produzione" class="ms-n1 me-2 w-25px">
                                                Corso editing e post produzione
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="divingGoProUsage" name="divingGoProUsage">
                                            <label class="form-check-label fs-sm" for="divingGoProUsage">
                                                <img src="{{ contextPath }}/fe/img/icons/gopro.svg" alt="Utilizzo GoPro o simili" class="ms-n1 me-2 w-25px">
                                                Utilizzo GoPro o simili
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Tipologie di vacanze\uscite -->
                                <div class="position-relative mb-3">
                                    <label for="divingVacationTypes" class="form-label">Tipologie di Vacanze/Uscite</label>
                                    <!-- Multiple select example -->
                                    <select class="form-select" id="divingVacationTypes" name="divingVacationTypes" data-select multiple aria-label="Tipologie di vacanze/uscite">
                                        <option value="">Seleziona tipologie</option>            
                                        <option value="blue-week">Settimana Blu</option>
                                        <option value="cruise">Crociera</option>                        
                                        <option value="half-day">Uscita half day</option>
                                        <option value="full-day">Uscita full day</option>
                                        <option value="weekend">Uscita weekend</option>
                                        <option value="tech-stage">Stage Tecnico</option>
                                        <option value="ccr-expedition">CCR Expedition</option>
                                        <option value="multi-location">Multi-Location Dive Trip</option>                
                                        <option value="other">Altro</option>
                                    </select>
                                </div>

                                <!-- Tipologia corsi -->
                                <div class="position-relative mb-3">
                                    <label for="divingRecCourses" class="form-label">Tipologia corsi</label>
                                    <!-- Multiple select example -->
                                    <select class="form-select" id="divingRecCourses" name="divingRecCourses" data-select multiple aria-label="Tipologia corsi">            
                                        <option value="">Seleziona corsi</option>
                                        <option value="padi-levels">PADI (1° 2° 3° livello)</option>
                                        <option value="ssi-levels">SSI (1° 2° 3° livello)</option>
                                        <option value="cmas-levels">CMAS (1° 2° 3° livello)</option>
                                        <option value="bsac-levels">BSAC (1° 2° 3° livello)</option>
                                        <option value="rescue-diver">Rescue Diver</option>
                                        <option value="specialty-courses">Specialty Courses</option>
                                        <option value="become-instructor">Diventa istruttore</option>
                                        <option value="become-guide">Diventa guida</option>
                                        <option value="mini-sub-kids">Mini sub (per ragazzi)</option>
                                    </select>
                                </div>

                                <!-- Tipologie di imbarcazioni -->
                                <div class="position-relative mb-3">
                                    <label class="form-label pb-1 mb-2">Tipologie di imbarcazioni</label>
                                    <div class="d-flex flex-column gap-2">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="divingRecInflatable" name="divingRecInflatable" checked>
                                            <label class="form-check-label fs-sm" for="divingRecInflatable">
                                                <img src="{{ contextPath }}/fe/img/icons/inflatable.svg" alt="Gommoni" class="ms-n1 me-2 w-25px">
                                                Gommoni
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="divingRecRigidBoat" name="divingRecRigidBoat">
                                            <label class="form-check-label fs-sm" for="divingRecRigidBoat">
                                                <img src="{{ contextPath }}/fe/img/icons/rigid-boat.svg" alt="Barca rigida" class="ms-n1 me-2 w-25px">
                                                Barca rigida
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="divingRecCruise" name="divingRecCruise">
                                            <label class="form-check-label fs-sm" for="divingRecCruise">
                                                <img src="{{ contextPath }}/fe/img/icons/cruise.svg" alt="Crociere" class="ms-n1 me-2 w-25px">
                                                Crociere
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="divingRecCatamaran" name="divingRecCatamaran">
                                            <label class="form-check-label fs-sm" for="divingRecCatamaran">
                                                <img src="{{ contextPath }}/fe/img/icons/catamaran.svg" alt="Catamarano" class="ms-n1 me-2 w-25px">
                                                Catamarano
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Gas supportati -->
                                <div class="position-relative mb-3">
                                    <label class="form-label pb-1 mb-2">Gas supportati</label>
                                    <div class="d-flex flex-column gap-2">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="divingTecAir" name="divingTecAir">
                                            <label class="form-check-label fs-sm" for="divingTecAir">
                                                <img src="{{ contextPath }}/fe/img/icons/air.svg" alt="Image" class="ms-n1 me-2 w-25px">
                                                Aria
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="divingTecNitrox40" name="divingTecNitrox40">
                                            <label class="form-check-label fs-sm" for="divingTecNitrox40">
                                                <img src="{{ contextPath }}/fe/img/icons/nitrox.svg" alt="Image" class="ms-n1 me-2 w-25px">
                                                Nitrox
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="divingTecAdvancedNitrox" name="divingTecAdvancedNitrox">
                                            <label class="form-check-label fs-sm" for="divingTecAdvancedNitrox">
                                                <img src="{{ contextPath }}/fe/img/icons/nitrox.svg" alt="Image" class="ms-n1 me-2 w-25px">
                                                Nitrox Avanzato
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="divingTecTrimixNormoxic" name="divingTecTrimixNormoxic">
                                            <label class="form-check-label fs-sm" for="divingTecTrimixNormoxic">
                                                <img src="{{ contextPath }}/fe/img/icons/trimix.svg" alt="Image" class="ms-n1 me-2 w-25px">
                                                Trimix Normossico
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="divingTecTrimixHypoxic" name="divingTecTrimixHypoxic">
                                            <label class="form-check-label fs-sm" for="divingTecTrimixHypoxic">
                                                <img src="{{ contextPath }}/fe/img/icons/trimix.svg" alt="Image" class="ms-n1 me-2 w-25px">
                                                Trimix Ipossico
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="divingTecPureOxygen" name="divingTecPureOxygen">
                                            <label class="form-check-label fs-sm" for="divingTecPureOxygen">
                                                <img src="{{ contextPath }}/fe/img/icons/oxygen.svg" alt="Image" class="ms-n1 me-2 w-25px">
                                                Ossigeno puro
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="divingTecHeliox" name="divingTecHeliox">
                                            <label class="form-check-label fs-sm" for="divingTecHeliox">
                                                <img src="{{ contextPath }}/fe/img/icons/heliox.svg" alt="Image" class="ms-n1 me-2 w-25px">
                                                Heliox
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Sistema di respirazione -->
                                <div class="position-relative mb-3">
                                    <label class="form-label pb-1 mb-2">Sistema di respirazione</label>
                                    <div class="d-flex flex-column gap-2">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="divingTecOpenCircuit" name="divingTecOpenCircuit">
                                            <label class="form-check-label fs-sm" for="divingTecOpenCircuit">
                                                <img src="{{ contextPath }}/fe/img/icons/open-circuit.svg" alt="Image" class="ms-n1 me-2 w-25px">
                                                Open Circuit
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="divingTecSemiClosedCircuit" name="divingTecSemiClosedCircuit">
                                            <label class="form-check-label fs-sm" for="divingTecSemiClosedCircuit">
                                                <img src="{{ contextPath }}/fe/img/icons/semi-closed-circuit.svg" alt="Image" class="ms-n1 me-2 w-25px">
                                                Semi Closed (SCR)
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="divingTecClosedCircuit" name="divingTecClosedCircuit">
                                            <label class="form-check-label fs-sm" for="divingTecClosedCircuit">
                                                <img src="{{ contextPath }}/fe/img/icons/closed-circuit.svg" alt="Image" class="ms-n1 me-2 w-25px">
                                                Closed Circuit (CCR)
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Configurazioni attrezzatura -->
                                <div class="position-relative mb-3">
                                    <label for="divingTecEquipmentConfiguration" class="form-label">Configurazioni attrezzatura</label><br>        
                                    <select class="form-select form-select-lg" data-select multiple aria-label="Configurazioni attrezzatura" name="divingTecEquipmentConfiguration" id="divingTecEquipmentConfiguration">
                                        <option value="">Seleziona configurazione</option>
                                        <option value="monobombola">Monobombola</option>
                                        <option value="bibombolaBackmount">Bibombola backmount</option>
                                        <option value="sidemount">Sidemount</option>
                                        <option value="stageDeco">Stage deco</option>
                                        <option value="bailoutCcr">Bailout (CCR)</option>
                                    </select> 
                                </div>

                                <!-- Profondità massima -->
                                <div class="position-relative mb-3">
                                    <label for="divingTecMaxDepth" class="form-label">Profondità massima</label><br>        
                                    <select class="form-select form-select-lg" aria-label="Profondità massima" name="divingTecMaxDepth" id="divingTecMaxDepth">
                                        <option value="">Seleziona profondità</option>
                                        <option value="40-60">40–60 m</option>
                                        <option value="60-100">60–100 m</option>
                                        <option value="100plus">100+ m</option>
                                    </select>
                                </div>

                                <!-- Varie -->
                                <div class="position-relative mb-3">
                                    <label class="form-label">Varie</label><br>        
                                    <div class="d-flex flex-column gap-2">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="divingEquipmentRental" name="divingEquipmentRental">
                                            <label class="form-check-label d-flex align-items-center fs-sm" for="divingEquipmentRental">
                                                Noleggio attrezzatura
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="divingEquipmentPurchase" name="divingEquipmentPurchase">
                                            <label class="form-check-label d-flex align-items-center fs-sm" for="divingEquipmentPurchase">
                                                Acquisto attrezzatura / Negozio
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="divingPrivateLessons" name="divingPrivateLessons">
                                            <label class="form-check-label d-flex align-items-center fs-sm" for="divingPrivateLessons">
                                                Lezioni private
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="divingEquipmentStorage" name="divingEquipmentStorage">
                                            <label class="form-check-label d-flex align-items-center fs-sm" for="divingEquipmentStorage">
                                                Deposito attrezzatura
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="divingRinseTanks" name="divingRinseTanks">
                                            <label class="form-check-label d-flex align-items-center fs-sm" for="divingRinseTanks">
                                                Vasche risciacquo
                                            </label>
                                        </div>
                                    </div>

                                </div>
                                                                        
                                
                            </div>

                            <!-- Freediving & Apnea -->
                            <div class="pb-4 mb-2 mb-xl-3">
                                <h4 class="h6"><img src="{{ contextPath }}/fe/img/account/apnea.svg" alt="Image" width="32" class="me-2"> Freediving & Apnea</h4>
                                
                                <!-- Tipologia corsi -->
                                <div class="position-relative mb-3">
                                    <label for="freedivingApneaCourses" class="form-label">Tipologia corsi</label>
                                    <!-- Multiple select example -->
                                    <select class="form-select" id="freedivingApneaCourses" name="freedivingApneaCourses" data-select multiple aria-label="Tipologia corsi">            
                                        <option value="apnea-academy">Apnea Academy (1° 2° 3° livello)</option>
                                        <option value="aida-international">AIDA International (1° 2° 3° livello)</option>
                                        <option value="ssi">SSI (1° 2° 3° livello)</option>
                                        <option value="instructor">Istruttore</option>            
                                    </select>
                                </div>
                                
                                <!-- Allenamenti in -->
                                <div class="position-relative mb-3">
                                    <label for="freedivingApneaCourses" class="form-label">Allenamenti in</label>
                                    <div class="d-flex flex-column gap-2">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="freedivingApneaTrainingPlacePool" name="freedivingApneaTrainingPlacePool">
                                            <label class="form-check-label fs-sm" for="freedivingApneaTrainingPlacePool">
                                                <img src="{{ contextPath }}/fe/img/icons/pool.svg" alt="Image" class="ms-n1 me-2 w-25px">
                                                Piscina
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="freedivingApneaTrainingPlaceLake" name="freedivingApneaTrainingPlaceLake">
                                            <label class="form-check-label fs-sm" for="freedivingApneaTrainingPlaceLake">
                                                <img src="{{ contextPath }}/fe/img/icons/lake.svg" alt="Image" class="ms-n1 me-2 w-25px">
                                                Lago
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="freedivingApneaTrainingPlaceSea" name="freedivingApneaTrainingPlaceSea">
                                            <label class="form-check-label fs-sm" for="freedivingApneaTrainingPlaceSea">
                                                <img src="{{ contextPath }}/fe/img/icons/sea.svg" alt="Image" class="ms-n1 me-2 w-25px">
                                                Mare
                                            </label>
                                        </div>
                                    </div>                                        
                                </div>
                                
                                <!-- Corsi per istruttori -->
                                <div class="position-relative mb-3">
                                    <label for="freedivingApneaInstructorCourses" class="form-label">Corsi per Istruttori</label>
                                    <select class="form-select" id="freedivingApneaInstructorCourses" name="freedivingApneaInstructorCourses" data-select multiple aria-label="Corsi per Istruttori">
                                        <option value="">Seleziona corso</option>
                                        <option value="freediving-instructor">Freediving Instructor (SSI / PADI / AIDA / AA / Molchanovs)</option>
                                        <option value="assistant-instructor">Assistant Instructor / Coach</option>
                                    </select>
                                </div>
                                
                                <!-- Specialità -->
                                <div class="position-relative mb-3">
                                    <label for="freedivingApneaSpecialties" class="form-label">Specialità</label>
                                    <select class="form-select" id="freedivingApneaSpecialties" name="freedivingApneaSpecialties" data-select multiple aria-label="Specialità">
                                        <option value="">Seleziona specialità</option>
                                        <option value="static-apnea">Apnea statica (STA)</option>
                                        <option value="dynamic-apnea">Apnea dinamica (DYN/DNF)</option>
                                        <option value="constant-weight">Assetto costante (CWT/CNF/FIM)</option>
                                        <option value="advanced-equalization">Compensazione avanzata (Equalization)</option>
                                        <option value="deep-equalization">Apnea profonda / Deep Equalization</option>
                                        <option value="mental-training">Allenamento mentale / Mental Training per apnea</option>
                                        <option value="monofin-noseclip-goggles">Monopinna e utilizzo tappa naso e occhialini</option>
                                        <option value="athlete-preparation">Preparazione atleti</option>
                                    </select>
                                </div>
                                
                                <!-- Workshop -->
                                <div class="position-relative mb-3">
                                    <label for="freedivingApneaWorkshops" class="form-label">Workshop</label>
                                    <select class="form-select" id="freedivingApneaWorkshops" name="freedivingApneaWorkshops" data-select multiple aria-label="Workshop">
                                        <option value="">Seleziona workshop</option>
                                        <option value="clinic-professional-athletes">Clinic con atleti professionisti</option>
                                        <option value="freediving-camp">Campi di allenamento apnea (Freediving Camp)</option>
                                        <option value="equalization-workshop">Workshop compensazione</option>
                                        <option value="yoga-apnea">Yoga & Apnea</option>
                                        <option value="rescue-safety">Rescue & Safety for Freedivers</option>
                                    </select>
                                </div>
                                
                                <!-- Stage -->
                                <div class="position-relative mb-3">
                                    <label for="freedivingApneaStages" class="form-label">Stage</label>
                                    <select class="form-select" id="freedivingApneaStages" name="freedivingApneaStages" data-select multiple aria-label="Stage">
                                        <option value="">Seleziona stage</option>
                                        <option value="multidisciplinary-stage">Stage Multidisciplinare</option>
                                        <option value="deep-equalization-stage">Stage Compensazione Profonda</option>
                                        <option value="static-apnea-stage">Stage di Apnea Statica (STA Camp)</option>
                                        <option value="constant-weight-stage">Stage Assetto Costante (CWT/CNF/FIM)</option>
                                        <option value="altitude-stage">Stage in Altitudine</option>
                                        <option value="athlete-stage">Stage per Atleti / Agonisti</option>
                                        <option value="monothematic-stage">Stage Monotematico (Tecnica, Sicurezza, Rescue, ecc.)</option>
                                        <option value="photo-biology-stage">Apnea & Fotografia Subacquea / Biologia Marina</option>
                                        <option value="instructor-assistant-stage">Stage per Istruttori / Assistenti</option>
                                    </select>
                                </div>
                                
                                <!-- Varie -->
                                <div class="position-relative mb-3">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="freedivingApneaYoga" name="freedivingApneaYoga">
                                        <label class="form-check-label d-flex align-items-center fs-sm" for="freedivingApneaYoga">
                                            Sessioni Yoga
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="freedivingApneaCertifiedInstructors" name="freedivingApneaCertifiedInstructors">
                                        <label class="form-check-label d-flex align-items-center fs-sm" for="freedivingApneaCertifiedInstructors">
                                            Allenatori certificati
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="freedivingApneaPhotoVideo" name="freedivingApneaPhotoVideo">
                                        <label class="form-check-label d-flex align-items-center fs-sm" for="freedivingApneaPhotoVideo">
                                            Foto e video
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="freedivingApneaEquipmentRental" name="freedivingApneaEquipmentRental">
                                        <label class="form-check-label d-flex align-items-center fs-sm" for="freedivingApneaEquipmentRental">
                                            Noleggio attrezzatura
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="freedivingApneaEquipmentPurchase" name="freedivingApneaEquipmentPurchase">
                                        <label class="form-check-label d-flex align-items-center fs-sm" for="freedivingApneaEquipmentPurchase">
                                            Acquisto attrezzatura \ Negozio
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Esperienze -->
                            <div class="pb-4 mb-2 mb-xl-3">
                                <h4 class="h6"><img src="{{ contextPath }}/fe/img/account/experience.svg" alt="Image" width="32" class="me-2"> Esperienze</h4>
                                
                                <div class="position-relative mb-3">
                                    <label class="form-label">Esperienze disponibili</label>
                                    <div class="d-flex flex-column gap-2">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="experienceSnorkeling" name="experienceSnorkeling">
                                            <label class="form-check-label fs-sm" for="experienceSnorkeling">
                                                <img src="{{ contextPath }}/fe/img/icons/snorkeling.svg" alt="Image" class="ms-n1 me-2 w-25px">
                                                Snorkeling
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="experienceBoatTrip" name="experienceBoatTrip">
                                            <label class="form-check-label fs-sm" for="experienceBoatTrip">
                                                <img src="{{ contextPath }}/fe/img/icons/boat-trip.svg" alt="Image" class="ms-n1 me-2 w-25px">
                                                Gite in barca
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="experienceTurtleWatching" name="experienceTurtleWatching">
                                            <label class="form-check-label fs-sm" for="experienceTurtleWatching">
                                                <img src="{{ contextPath }}/fe/img/icons/turtle.svg" alt="Image" class="ms-n1 me-2 w-25px">
                                                Avvistamento Tartarughe
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="experienceDolphinWatching" name="experienceDolphinWatching">
                                            <label class="form-check-label fs-sm" for="experienceDolphinWatching">
                                                <img src="{{ contextPath }}/fe/img/icons/dolphin.svg" alt="Image" class="ms-n1 me-2 w-25px">
                                                Avvistamento Delfini
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="experienceWhaleWatching" name="experienceWhaleWatching">
                                            <label class="form-check-label fs-sm" for="experienceWhaleWatching">
                                                <img src="{{ contextPath }}/fe/img/icons/whale.svg" alt="Image" class="ms-n1 me-2 w-25px">
                                                Avvistamento Balene
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="experienceSunsetTrip" name="experienceSunsetTrip">
                                            <label class="form-check-label fs-sm" for="experienceSunsetTrip">
                                                <img src="{{ contextPath }}/fe/img/icons/sunset-trip.svg" alt="Image" class="ms-n1 me-2 w-25px">
                                                Gita al tramonto
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="experienceIsletExcursion" name="experienceIsletExcursion">
                                            <label class="form-check-label fs-sm" for="experienceIsletExcursion">
                                                <img src="{{ contextPath }}/fe/img/icons/islet-excursion.svg" alt="Image" class="ms-n1 me-2 w-25px">
                                                Escursione su isolotti
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="experienceNatureTour" name="experienceNatureTour">
                                            <label class="form-check-label fs-sm" for="experienceNatureTour">
                                                <img src="{{ contextPath }}/fe/img/icons/nature-tour.svg" alt="Image" class="ms-n1 me-2 w-25px">
                                                Tour naturalistico
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="experienceBoatYoga" name="experienceBoatYoga">
                                            <label class="form-check-label fs-sm" for="experienceBoatYoga">
                                                <img src="{{ contextPath }}/fe/img/icons/boat-yoga.svg" alt="Image" class="ms-n1 me-2 w-25px">
                                                Yoga in barca
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="experienceNightBoatTrip" name="experienceNightBoatTrip">
                                            <label class="form-check-label fs-sm" for="experienceNightBoatTrip">
                                                <img src="{{ contextPath }}/fe/img/icons/night-boat.svg" alt="Image" class="ms-n1 me-2 w-25px">
                                                Notturna in barca
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="experienceDinnerTasting" name="experienceDinnerTasting">
                                            <label class="form-check-label fs-sm" for="experienceDinnerTasting">
                                                <img src="{{ contextPath }}/fe/img/icons/dinner-tasting.svg" alt="Image" class="ms-n1 me-2 w-25px">
                                                Degustazione / Cena in Barca
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="experiencePhotoTour" name="experiencePhotoTour">
                                            <label class="form-check-label fs-sm" for="experiencePhotoTour">
                                                <img src="{{ contextPath }}/fe/img/icons/photo-tour.svg" alt="Image" class="ms-n1 me-2 w-25px">
                                                Tour fotografico
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="position-relative mb-3">
                                    <div class="d-flex flex-column gap-2">
                                        <label class="form-label">Tour privati</label>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="experiencePrivateTours" name="experiencePrivateTours">
                                            <label class="form-check-label d-flex align-items-center fs-sm" for="experiencePrivateTours">
                                                Tour privati
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                            </div>
                            
                            <!-- Sport -->
                            <div class="pb-4 mb-2 mb-xl-3">
                                <h4 class="h6"><img src="{{ contextPath }}/fe/img/account/sport.svg" alt="Image" width="32" class="me-2"> Sport</h4>
                                
                                <!-- Tipi di sport offerti -->
                                <div data-filter-list="{&quot;searchClass&quot;: &quot;sport-search&quot;, &quot;listClass&quot;: &quot;sport-list&quot;, &quot;valueNames&quot;: [&quot;form-check-label&quot;]}">
                                    <div class="position-relative mb-3">
                                        <label class="form-label">Tipi di sport offerti</label>
                                        <div class="position-relative">
                                            <i class="fi-search position-absolute top-50 start-0 translate-middle-y ms-3"></i>
                                            <input type="search" class="sport-search form-control form-icon-start" placeholder="Cerca tipi di sport">
                                        </div>
                                    </div>
                                    <div class="position-relative mb-4" style="height: 215px" data-simplebar="" data-simplebar-auto-hide="false">
                                    <div class="sport-list d-flex flex-column gap-2">
                                        <div class="form-check mb-0">
                                            <input type="checkbox" class="form-check-input" id="sportSurf" name="sportSurf">
                                            <label class="form-check-label" for="sportSurf">
                                                <img src="{{ contextPath }}/fe/img/icons/surf.svg" alt="Image" class="w-25px">
                                                Surf
                                            </label>
                                        </div>
                                        <div class="form-check mb-0">
                                            <input type="checkbox" class="form-check-input" id="sportWindsurf" name="sportWindsurf">
                                            <label class="form-check-label" for="sportWindsurf">
                                                <img src="{{ contextPath }}/fe/img/icons/windsurf.svg" alt="Image" class="w-25px">
                                                Windsurf
                                            </label>
                                        </div>
                                        <div class="form-check mb-0">
                                            <input type="checkbox" class="form-check-input" id="sportKitesurf" name="sportKitesurf">
                                            <label class="form-check-label" for="sportKitesurf">
                                                <img src="{{ contextPath }}/fe/img/icons/kitesurf.svg" alt="Image" class="w-25px">
                                                Kitesurf
                                            </label>
                                        </div>
                                        <div class="form-check mb-0">
                                            <input type="checkbox" class="form-check-input" id="sportSup" name="sportSup">
                                            <label class="form-check-label" for="sportSup">
                                                <img src="{{ contextPath }}/fe/img/icons/sup.svg" alt="Image" class="w-25px">
                                                SUP
                                            </label>
                                        </div>
                                        <div class="form-check mb-0">
                                            <input type="checkbox" class="form-check-input" id="sportKayak" name="sportKayak">
                                            <label class="form-check-label" for="sportKayak">
                                                <img src="{{ contextPath }}/fe/img/icons/kayak.svg" alt="Image" class="w-25px">
                                                Kayak
                                            </label>
                                        </div>
                                        <div class="form-check mb-0">
                                            <input type="checkbox" class="form-check-input" id="sportCanoe" name="sportCanoe">                
                                            <label class="form-check-label" for="sportCanoe">
                                                <img src="{{ contextPath }}/fe/img/icons/canoe.svg" alt="Image" class="w-25px">
                                                Canoa
                                            </label>
                                        </div>
                                        <div class="form-check mb-0">
                                            <input type="checkbox" class="form-check-input" id="sportBodyboard" name="sportBodyboard">                
                                            <label class="form-check-label" for="sportBodyboard">
                                                <img src="{{ contextPath }}/fe/img/icons/bodyboard.svg" alt="Image" class="w-25px">
                                                Bodyboard
                                            </label>
                                        </div>
                                        <div class="form-check mb-0">
                                            <input type="checkbox" class="form-check-input" id="sportWakeboard" name="sportWakeboard">                
                                            <label class="form-check-label" for="sportWakeboard">
                                                <img src="{{ contextPath }}/fe/img/icons/wakeboard.svg" alt="Image" class="w-25px">
                                                Wakeboard
                                            </label>
                                        </div>
                                        <div class="form-check mb-0">
                                            <input type="checkbox" class="form-check-input" id="sportSkimboard" name="sportSkimboard">                
                                            <label class="form-check-label" for="sportSkimboard">
                                                <img src="{{ contextPath }}/fe/img/icons/skimboard.svg" alt="Image" class="w-25px">
                                                Skimboard
                                            </label>
                                        </div>
                                        <div class="form-check mb-0">
                                            <input type="checkbox" class="form-check-input" id="sportParasailing" name="sportParasailing">                
                                            <label class="form-check-label" for="sportParasailing">
                                                <img src="{{ contextPath }}/fe/img/icons/parasailing.svg" alt="Image" class="w-25px">
                                                Parasailing
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                </div>

                                <div class="position-relative">
                                    <label class="form-label">Varie</label><br>
                                    <div class="d-flex flex-column gap-2">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="sportEquipmentRental" name="sportEquipmentRental">
                                            <label class="form-check-label d-flex align-items-center fs-sm" for="sportEquipmentRental">
                                                Noleggio attrezzatura
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="sportEquipmentPurchase" name="sportEquipmentPurchase">
                                            <label class="form-check-label d-flex align-items-center fs-sm" for="sportEquipmentPurchase">
                                                Acquisto attrezzatura / Negozio
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="sportCourses" name="sportCourses">
                                            <label class="form-check-label d-flex align-items-center fs-sm" for="sportCourses">
                                                Corsi
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="sportPrivateLessons" name="sportPrivateLessons">
                                            <label class="form-check-label d-flex align-items-center fs-sm" for="sportPrivateLessons">
                                                Lezioni private
                                            </label>
                                        </div>
                                    </div>

                                </div>
                                
                            </div>
                            
                            <!-- Varie -->
                            <div class="pb-4 mb-2 mb-xl-3">
                                <h4 class="h6"><img src="{{ contextPath }}/fe/img/account/misc.svg" alt="Image" width="32" class="me-2"> Varie</h4>
                                
                                <div class="position-relative">                                    
                                    <label for="miscLargeGroupsAvailability" class="form-label">Disponibilità grandi gruppi</label>
                                    <select class="form-select form-select-lg mb-3" id="miscLargeGroupsAvailability" name="miscLargeGroupsAvailability">
                                        <option value="">Seleziona disponibilità</option>
                                        <option value="15-50">Da 15 a 50 persone</option>
                                        <option value="50plus">Sopra 50 persone</option>
                                        <option value="no">No</option>
                                    </select>                                    

                                    <div class="d-flex flex-column gap-2">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="miscLargeGroupsDiscount" name="miscLargeGroupsDiscount">
                                            <label class="form-check-label d-flex align-items-center fs-sm" for="miscLargeGroupsDiscount">
                                                Scontistica grandi gruppi
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="miscWifi" name="miscWifi">
                                            <label class="form-check-label d-flex align-items-center fs-sm" for="miscWifi">
                                                WiFi
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="miscChangeRoomsShowers" name="miscChangeRoomsShowers">
                                            <label class="form-check-label d-flex align-items-center fs-sm" for="miscChangeRoomsShowers">
                                                Spogliatoi / Docce
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="miscRelaxArea" name="miscRelaxArea">
                                            <label class="form-check-label d-flex align-items-center fs-sm" for="miscRelaxArea">
                                                Zona relax / Bar
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="miscFreeWater" name="miscFreeWater">
                                            <label class="form-check-label d-flex align-items-center fs-sm" for="miscFreeWater">
                                                Acqua gratuita
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="miscEmergencyKit" name="miscEmergencyKit">
                                            <label class="form-check-label d-flex align-items-center fs-sm" for="miscEmergencyKit">
                                                Kit emergenza / Camera iperbarica
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="miscHotelRestaurantDeals" name="miscHotelRestaurantDeals">
                                            <label class="form-check-label d-flex align-items-center fs-sm" for="miscHotelRestaurantDeals">
                                                Convenzioni hotel / ristoranti
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="miscTransfer" name="miscTransfer">
                                            <label class="form-check-label d-flex align-items-center fs-sm" for="miscTransfer">
                                                Transfer
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Valutazione -->
                            <div class="pb-2 pb-lg-0">
                                <h4 class="h6">Valutazione</h4>
                                <div class="d-flex flex-column gap-2">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="star-5" checked="">
                                        <label class="form-check-label d-flex align-items-center fs-sm" for="star-5">
                                            5 <i class="fi-star-filled text-warning ms-1"></i>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="star-4" checked="">
                                        <label class="form-check-label d-flex align-items-center fs-sm" for="star-4">
                                            4 <i class="fi-star-filled text-warning ms-1"></i>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="star-3">
                                        <label class="form-check-label d-flex align-items-center fs-sm" for="star-3">
                                            3 <i class="fi-star-filled text-warning ms-1"></i>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="star-2">
                                        <label class="form-check-label d-flex align-items-center fs-sm" for="star-2">
                                            2-1 <i class="fi-star-filled text-warning ms-1"></i>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </aside>


                <!-- Listings grid -->
                <div class="col-lg-9">

                    <!-- Sort selector + Map view toggle -->
                    <div class="d-flex align-items-center gap-4 pb-3 mb-2 mb-xl-3">
                        <div class="fs-sm text-nowrap d-none d-md-inline">Trovati 116 resulti</div>
                        <div class="position-relative ms-md-auto" style="width: 170px">
                            <i class="fi-sort position-absolute top-50 start-0 translate-middle-y z-1 ms-3"></i>
                            <select class="form-select form-icon-start rounded-pill" data-select="{
                                    &quot;removeItemButton&quot;: false,
                                    &quot;classNames&quot;: {
                                    &quot;containerInner&quot;: [&quot;form-select&quot;, &quot;form-icon-start&quot;, &quot;rounded-pill&quot;]
                                    }
                                    }">
                                <option value="Popular">Popolari</option>
                                <option value="Rating">Valutazione</option>                                
                            </select>
                        </div>
                        <div class="nav ms-auto ms-md-0">
                            <a class="nav-link position-relative p-0" href="#map" data-bs-toggle="offcanvas">
                                <i class="fi-map fs-base me-2"></i>
                                <span class="hover-effect-underline stretched-link">Vedi sulla mappa</span>
                            </a>
                        </div>
                    </div>

                    <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-2 row-cols-xl-3 g-4 g-sm-3 g-lg-4">

                        {% for business in lookup("Business", checkPublished=false, language='false') %}
                        <!-- Listing -->
                        <div class="col">
                            <article class="card h-100 hover-effect-scale hover-effect-opacity bg-transparent">
                                <div class="card-img-top position-relative bg-body-tertiary overflow-hidden">
                                    <div class="ratio hover-effect-target"
                                         style="--fn-aspect-ratio: calc(198 / 304 * 100%)">
                                        <img src="{{ routes('BE_IMAGE') }}?oid={{ business.imageIds[0] }}" alt="{{ business.fullname }}">
                                    </div>
                                    <div class="position-absolute top-0 end-0 z-2 hover-effect-target opacity-0 pt-1 pt-sm-0 pe-1 pe-sm-0 mt-2 mt-sm-3 me-2 me-sm-3">
                                        <button type="button"
                                                class="btn btn-sm btn-icon btn-light bg-light border-0 rounded-circle animate-pulse"
                                                aria-label="Add to wishlist">
                                            <i class="fi-heart animate-target fs-sm"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body pt-3 pb-2 px-3">
                                    <span class="badge text-body-emphasis bg-secondary-subtle text-decoration-none mb-2">{{ business.category }}</span>
                                    <h3 class="h5 pt-1 mb-2">
                                        <a class="hover-effect-underline stretched-link" href="{{ business.url }}">{{ business.fullname }}</a>
                                    </h3>
                                    <p class="fs-sm mb-0">{{ business.description }}</p>
                                </div>
                                <div class="card-footer bg-transparent border-0 pt-0 pb-3 px-3">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex align-items-center gap-1">
                                            <i class="fi-star-filled text-warning"></i>
                                            <span class="fs-sm text-secondary-emphasis">{{ business.rating }}</span>
                                            <span class="fs-xs text-body-secondary align-self-end">({{ business.reviewCount }})</span>
                                        </div>
                                        <div class="d-flex align-items-center gap-1 min-w-0 fs-sm">
                                            <i class="fi-map-pin"></i>
                                            <span class="text-truncate">{{ business.distance }} km from center</span>
                                        </div>
                                    </div>
                                    <div class="h6 pt-3 mb-0">${{ business.price }}</div>
                                </div>
                            </article>
                        </div>
                        {% endfor %}

                        <!-- Listing -->
                        <div class="col">
                            <article class="card h-100 hover-effect-scale hover-effect-opacity bg-transparent">
                                <div class="card-img-top position-relative bg-body-tertiary overflow-hidden">
                                    <div class="ratio hover-effect-target" style="--fn-aspect-ratio: calc(198 / 304 * 100%)">
                                        <img src="assets/img/listings/city-guide/v1/02.jpg" alt="Image">
                                    </div>
                                    <div class="position-absolute top-0 end-0 z-2 hover-effect-target opacity-0 pt-1 pt-sm-0 pe-1 pe-sm-0 mt-2 mt-sm-3 me-2 me-sm-3">
                                        <button type="button" class="btn btn-sm btn-icon btn-light bg-light border-0 rounded-circle animate-pulse" aria-label="Add to wishlist">
                                            <i class="fi-heart animate-target fs-sm"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body pt-3 pb-2 px-3">
                                    <span class="badge text-body-emphasis bg-secondary-subtle text-decoration-none mb-2">Entertainment</span>
                                    <h3 class="h5 pt-1 mb-2">
                                        <a class="hover-effect-underline stretched-link" href="single-entry-city-guide.html">Mountain Lake Tour</a>
                                    </h3>
                                    <p class="fs-sm mb-0">Enjoy breathtaking views, fresh air, and a peaceful escape into the wilderness.</p>
                                </div>
                                <div class="card-footer bg-transparent border-0 pt-0 pb-3 px-3">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex align-items-center gap-1">
                                            <i class="fi-star-filled text-warning"></i>
                                            <span class="fs-sm text-secondary-emphasis">4.5</span>
                                            <span class="fs-xs text-body-secondary align-self-end">(214)</span>
                                        </div>
                                        <div class="d-flex align-items-center gap-1 min-w-0 fs-sm">
                                            <i class="fi-map-pin"></i>
                                            <span class="text-truncate">13 km from center</span>
                                        </div>
                                    </div>
                                    <div class="h6 pt-3 mb-0">$60</div>
                                </div>
                            </article>
                        </div>

                        <!-- Listing -->
                        <div class="col">
                            <article class="card h-100 hover-effect-scale hover-effect-opacity bg-transparent">
                                <div class="card-img-top position-relative bg-body-tertiary overflow-hidden">
                                    <div class="ratio hover-effect-target" style="--fn-aspect-ratio: calc(198 / 304 * 100%)">
                                        <img src="assets/img/listings/city-guide/v1/03.jpg" alt="Image">
                                    </div>
                                    <div class="position-absolute top-0 end-0 z-2 hover-effect-target opacity-0 pt-1 pt-sm-0 pe-1 pe-sm-0 mt-2 mt-sm-3 me-2 me-sm-3">
                                        <button type="button" class="btn btn-sm btn-icon btn-light bg-light border-0 rounded-circle animate-pulse" aria-label="Add to wishlist">
                                            <i class="fi-heart animate-target fs-sm"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body pt-3 pb-2 px-3">
                                    <span class="badge text-body-emphasis bg-secondary-subtle text-decoration-none mb-2">Entertainment</span>
                                    <h3 class="h5 pt-1 mb-2">
                                        <a class="hover-effect-underline stretched-link" href="single-entry-city-guide.html">Jeep Tour with 4x4 Club</a>
                                    </h3>
                                    <p class="fs-sm mb-0">Explore the wild side of Barcelona in our reliable off-road 4x4 vehicle!</p>
                                </div>
                                <div class="card-footer bg-transparent border-0 pt-0 pb-3 px-3">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex align-items-center gap-1">
                                            <i class="fi-star-filled text-warning"></i>
                                            <span class="fs-sm text-secondary-emphasis">4.7</span>
                                            <span class="fs-xs text-body-secondary align-self-end">(185)</span>
                                        </div>
                                        <div class="d-flex align-items-center gap-1 min-w-0 fs-sm">
                                            <i class="fi-map-pin"></i>
                                            <span class="text-truncate">9.8 km from center</span>
                                        </div>
                                    </div>
                                    <div class="h6 pt-3 mb-0">$130</div>
                                </div>
                            </article>
                        </div>

                        <!-- Listing -->
                        <div class="col">
                            <article class="card h-100 hover-effect-scale hover-effect-opacity bg-transparent">
                                <div class="card-img-top position-relative bg-body-tertiary overflow-hidden">
                                    <div class="ratio hover-effect-target" style="--fn-aspect-ratio: calc(198 / 304 * 100%)">
                                        <img src="assets/img/listings/city-guide/v1/04.jpg" alt="Image">
                                    </div>
                                    <div class="position-absolute top-0 end-0 z-2 hover-effect-target opacity-0 pt-1 pt-sm-0 pe-1 pe-sm-0 mt-2 mt-sm-3 me-2 me-sm-3">
                                        <button type="button" class="btn btn-sm btn-icon btn-light bg-light border-0 rounded-circle animate-pulse" aria-label="Add to wishlist">
                                            <i class="fi-heart animate-target fs-sm"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body pt-3 pb-2 px-3">
                                    <span class="badge text-body-emphasis bg-secondary-subtle text-decoration-none mb-2">Entertainment</span>
                                    <h3 class="h5 pt-1 mb-2">
                                        <a class="hover-effect-underline stretched-link" href="single-entry-city-guide.html">Sky Views Observatory</a>
                                    </h3>
                                    <p class="fs-sm mb-0">Take in breathtaking skyline views from an unparalleled vantage point.</p>
                                </div>
                                <div class="card-footer bg-transparent border-0 pt-0 pb-3 px-3">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex align-items-center gap-1">
                                            <i class="fi-star-filled text-warning"></i>
                                            <span class="fs-sm text-secondary-emphasis">4.3</span>
                                            <span class="fs-xs text-body-secondary align-self-end">(3462)</span>
                                        </div>
                                        <div class="d-flex align-items-center gap-1 min-w-0 fs-sm">
                                            <i class="fi-map-pin"></i>
                                            <span class="text-truncate">1.7 km from center</span>
                                        </div>
                                    </div>
                                    <div class="h6 pt-3 mb-0">$5</div>
                                </div>
                            </article>
                        </div>

                        <!-- Listing -->
                        <div class="col">
                            <article class="card h-100 hover-effect-scale hover-effect-opacity bg-transparent">
                                <div class="card-img-top position-relative bg-body-tertiary overflow-hidden">
                                    <div class="ratio hover-effect-target" style="--fn-aspect-ratio: calc(198 / 304 * 100%)">
                                        <img src="assets/img/listings/city-guide/v1/05.jpg" alt="Image">
                                    </div>
                                    <div class="position-absolute top-0 end-0 z-2 hover-effect-target opacity-0 pt-1 pt-sm-0 pe-1 pe-sm-0 mt-2 mt-sm-3 me-2 me-sm-3">
                                        <button type="button" class="btn btn-sm btn-icon btn-light bg-light border-0 rounded-circle animate-pulse" aria-label="Add to wishlist">
                                            <i class="fi-heart animate-target fs-sm"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body pt-3 pb-2 px-3">
                                    <span class="badge text-body-emphasis bg-secondary-subtle text-decoration-none mb-2">Entertainment</span>
                                    <h3 class="h5 pt-1 mb-2">
                                        <a class="hover-effect-underline stretched-link" href="single-entry-city-guide.html">Museum of Illusions</a>
                                    </h3>
                                    <p class="fs-sm mb-0">Challenge your perception with mind-bending and interactive exhibits.</p>
                                </div>
                                <div class="card-footer bg-transparent border-0 pt-0 pb-3 px-3">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex align-items-center gap-1">
                                            <i class="fi-star-filled text-warning"></i>
                                            <span class="fs-sm text-secondary-emphasis">4.6</span>
                                            <span class="fs-xs text-body-secondary align-self-end">(1572)</span>
                                        </div>
                                        <div class="d-flex align-items-center gap-1 min-w-0 fs-sm">
                                            <i class="fi-map-pin"></i>
                                            <span class="text-truncate">2.3 km from center</span>
                                        </div>
                                    </div>
                                    <div class="h6 pt-3 mb-0">$35</div>
                                </div>
                            </article>
                        </div>

                        <!-- Listing -->
                        <div class="col">
                            <article class="card h-100 hover-effect-scale hover-effect-opacity bg-transparent">
                                <div class="card-img-top position-relative bg-body-tertiary overflow-hidden">
                                    <div class="ratio hover-effect-target" style="--fn-aspect-ratio: calc(198 / 304 * 100%)">
                                        <img src="assets/img/listings/city-guide/v1/06.jpg" alt="Image">
                                    </div>
                                    <div class="position-absolute top-0 end-0 z-2 hover-effect-target opacity-0 pt-1 pt-sm-0 pe-1 pe-sm-0 mt-2 mt-sm-3 me-2 me-sm-3">
                                        <button type="button" class="btn btn-sm btn-icon btn-light bg-light border-0 rounded-circle animate-pulse" aria-label="Add to wishlist">
                                            <i class="fi-heart animate-target fs-sm"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body pt-3 pb-2 px-3">
                                    <span class="badge text-body-emphasis bg-secondary-subtle text-decoration-none mb-2">Entertainment</span>
                                    <h3 class="h5 pt-1 mb-2">
                                        <a class="hover-effect-underline stretched-link" href="single-entry-city-guide.html">Barcelona Oceanarium</a>
                                    </h3>
                                    <p class="fs-sm mb-0">Enter a world of aquatic discovery at one of the biggest aquariums in Europe.</p>
                                </div>
                                <div class="card-footer bg-transparent border-0 pt-0 pb-3 px-3">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex align-items-center gap-1">
                                            <i class="fi-star-filled text-warning"></i>
                                            <span class="fs-sm text-secondary-emphasis">4.7</span>
                                            <span class="fs-xs text-body-secondary align-self-end">(8325)</span>
                                        </div>
                                        <div class="d-flex align-items-center gap-1 min-w-0 fs-sm">
                                            <i class="fi-map-pin"></i>
                                            <span class="text-truncate">1.8 km from center</span>
                                        </div>
                                    </div>
                                    <div class="h6 pt-3 mb-0">$40</div>
                                </div>
                            </article>
                        </div>

                        <!-- Listing -->
                        <div class="col">
                            <article class="card h-100 hover-effect-scale hover-effect-opacity bg-transparent">
                                <div class="card-img-top position-relative bg-body-tertiary overflow-hidden">
                                    <div class="ratio hover-effect-target" style="--fn-aspect-ratio: calc(198 / 304 * 100%)">
                                        <img src="assets/img/listings/city-guide/v1/07.jpg" alt="Image">
                                    </div>
                                    <div class="position-absolute top-0 end-0 z-2 hover-effect-target opacity-0 pt-1 pt-sm-0 pe-1 pe-sm-0 mt-2 mt-sm-3 me-2 me-sm-3">
                                        <button type="button" class="btn btn-sm btn-icon btn-light bg-light border-0 rounded-circle animate-pulse" aria-label="Add to wishlist">
                                            <i class="fi-heart animate-target fs-sm"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body pt-3 pb-2 px-3">
                                    <span class="badge text-body-emphasis bg-secondary-subtle text-decoration-none mb-2">Entertainment</span>
                                    <h3 class="h5 pt-1 mb-2">
                                        <a class="hover-effect-underline stretched-link" href="single-entry-city-guide.html">Art &amp; Design Museum</a>
                                    </h3>
                                    <p class="fs-sm mb-0">Europe's most extensive collection of modern and contemporary art.</p>
                                </div>
                                <div class="card-footer bg-transparent border-0 pt-0 pb-3 px-3">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex align-items-center gap-1">
                                            <i class="fi-star-filled text-warning"></i>
                                            <span class="fs-sm text-secondary-emphasis">4.9</span>
                                            <span class="fs-xs text-body-secondary align-self-end">(2078)</span>
                                        </div>
                                        <div class="d-flex align-items-center gap-1 min-w-0 fs-sm">
                                            <i class="fi-map-pin"></i>
                                            <span class="text-truncate">1.4 km from center</span>
                                        </div>
                                    </div>
                                    <div class="h6 pt-3 mb-0">$15</div>
                                </div>
                            </article>
                        </div>

                        <!-- Listing -->
                        <div class="col">
                            <article class="card h-100 hover-effect-scale hover-effect-opacity bg-transparent">
                                <div class="card-img-top position-relative bg-body-tertiary overflow-hidden">
                                    <div class="ratio hover-effect-target" style="--fn-aspect-ratio: calc(198 / 304 * 100%)">
                                        <img src="assets/img/listings/city-guide/v1/08.jpg" alt="Image">
                                    </div>
                                    <div class="position-absolute top-0 end-0 z-2 hover-effect-target opacity-0 pt-1 pt-sm-0 pe-1 pe-sm-0 mt-2 mt-sm-3 me-2 me-sm-3">
                                        <button type="button" class="btn btn-sm btn-icon btn-light bg-light border-0 rounded-circle animate-pulse" aria-label="Add to wishlist">
                                            <i class="fi-heart animate-target fs-sm"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body pt-3 pb-2 px-3">
                                    <span class="badge text-body-emphasis bg-secondary-subtle text-decoration-none mb-2">Entertainment</span>
                                    <h3 class="h5 pt-1 mb-2">
                                        <a class="hover-effect-underline stretched-link" href="single-entry-city-guide.html">Tibidabo Ferris Wheel</a>
                                    </h3>
                                    <p class="fs-sm mb-0">Atop the Tibidabo Entertainment Park, you can enjoy a spot of sightseeing.</p>
                                </div>
                                <div class="card-footer bg-transparent border-0 pt-0 pb-3 px-3">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex align-items-center gap-1">
                                            <i class="fi-star-filled text-warning"></i>
                                            <span class="fs-sm text-secondary-emphasis">4.4</span>
                                            <span class="fs-xs text-body-secondary align-self-end">(1059)</span>
                                        </div>
                                        <div class="d-flex align-items-center gap-1 min-w-0 fs-sm">
                                            <i class="fi-map-pin"></i>
                                            <span class="text-truncate">3.6 km from center</span>
                                        </div>
                                    </div>
                                    <div class="h6 pt-3 mb-0">$10</div>
                                </div>
                            </article>
                        </div>

                        <!-- Listing -->
                        <div class="col">
                            <article class="card h-100 hover-effect-scale hover-effect-opacity bg-transparent">
                                <div class="card-img-top position-relative bg-body-tertiary overflow-hidden">
                                    <div class="ratio hover-effect-target" style="--fn-aspect-ratio: calc(198 / 304 * 100%)">
                                        <img src="assets/img/listings/city-guide/v1/09.jpg" alt="Image">
                                    </div>
                                    <div class="position-absolute top-0 end-0 z-2 hover-effect-target opacity-0 pt-1 pt-sm-0 pe-1 pe-sm-0 mt-2 mt-sm-3 me-2 me-sm-3">
                                        <button type="button" class="btn btn-sm btn-icon btn-light bg-light border-0 rounded-circle animate-pulse" aria-label="Add to wishlist">
                                            <i class="fi-heart animate-target fs-sm"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body pt-3 pb-2 px-3">
                                    <span class="badge text-body-emphasis bg-secondary-subtle text-decoration-none mb-2">Entertainment</span>
                                    <h3 class="h5 pt-1 mb-2">
                                        <a class="hover-effect-underline stretched-link" href="single-entry-city-guide.html">VRFun Virtual Reality Park</a>
                                    </h3>
                                    <p class="fs-sm mb-0">Immersive virtual reality park offering stunning VR experiences for all ages.</p>
                                </div>
                                <div class="card-footer bg-transparent border-0 pt-0 pb-3 px-3">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex align-items-center gap-1">
                                            <i class="fi-star-filled text-warning"></i>
                                            <span class="fs-sm text-secondary-emphasis">4.9</span>
                                            <span class="fs-xs text-body-secondary align-self-end">(112)</span>
                                        </div>
                                        <div class="d-flex align-items-center gap-1 min-w-0 fs-sm">
                                            <i class="fi-map-pin"></i>
                                            <span class="text-truncate">2.1 km from center</span>
                                        </div>
                                    </div>
                                    <div class="h6 pt-3 mb-0">$25</div>
                                </div>
                            </article>
                        </div>

                        <!-- Listing -->
                        <div class="col">
                            <article class="card h-100 hover-effect-scale hover-effect-opacity bg-transparent">
                                <div class="card-img-top position-relative bg-body-tertiary overflow-hidden">
                                    <div class="ratio hover-effect-target" style="--fn-aspect-ratio: calc(198 / 304 * 100%)">
                                        <img src="assets/img/listings/city-guide/v1/10.jpg" alt="Image">
                                    </div>
                                    <div class="position-absolute top-0 end-0 z-2 hover-effect-target opacity-0 pt-1 pt-sm-0 pe-1 pe-sm-0 mt-2 mt-sm-3 me-2 me-sm-3">
                                        <button type="button" class="btn btn-sm btn-icon btn-light bg-light border-0 rounded-circle animate-pulse" aria-label="Add to wishlist">
                                            <i class="fi-heart animate-target fs-sm"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body pt-3 pb-2 px-3">
                                    <span class="badge text-body-emphasis bg-secondary-subtle text-decoration-none mb-2">Entertainment</span>
                                    <h3 class="h5 pt-1 mb-2">
                                        <a class="hover-effect-underline stretched-link" href="single-entry-city-guide.html">La Sagrada Familia</a>
                                    </h3>
                                    <p class="fs-sm mb-0">Antoni Gaudí's masterpiece features stunning design and intricate details.</p>
                                </div>
                                <div class="card-footer bg-transparent border-0 pt-0 pb-3 px-3">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex align-items-center gap-1">
                                            <i class="fi-star-filled text-warning"></i>
                                            <span class="fs-sm text-secondary-emphasis">4.8</span>
                                            <span class="fs-xs text-body-secondary align-self-end">(12694)</span>
                                        </div>
                                        <div class="d-flex align-items-center gap-1 min-w-0 fs-sm">
                                            <i class="fi-map-pin"></i>
                                            <span class="text-truncate">0.5 km from center</span>
                                        </div>
                                    </div>
                                    <div class="h6 pt-3 mb-0">$30</div>
                                </div>
                            </article>
                        </div>

                        <!-- Listing -->
                        <div class="col">
                            <article class="card h-100 hover-effect-scale hover-effect-opacity bg-transparent">
                                <div class="card-img-top position-relative bg-body-tertiary overflow-hidden">
                                    <div class="ratio hover-effect-target" style="--fn-aspect-ratio: calc(198 / 304 * 100%)">
                                        <img src="assets/img/listings/city-guide/v1/11.jpg" alt="Image">
                                    </div>
                                    <div class="position-absolute top-0 end-0 z-2 hover-effect-target opacity-0 pt-1 pt-sm-0 pe-1 pe-sm-0 mt-2 mt-sm-3 me-2 me-sm-3">
                                        <button type="button" class="btn btn-sm btn-icon btn-light bg-light border-0 rounded-circle animate-pulse" aria-label="Add to wishlist">
                                            <i class="fi-heart animate-target fs-sm"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body pt-3 pb-2 px-3">
                                    <span class="badge text-body-emphasis bg-secondary-subtle text-decoration-none mb-2">Entertainment</span>
                                    <h3 class="h5 pt-1 mb-2">
                                        <a class="hover-effect-underline stretched-link" href="single-entry-city-guide.html">City Guided Tour</a>
                                    </h3>
                                    <p class="fs-sm mb-0">Embark on an interactive city tour around central Barcelona with a guide.</p>
                                </div>
                                <div class="card-footer bg-transparent border-0 pt-0 pb-3 px-3">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex align-items-center gap-1">
                                            <i class="fi-star-filled text-warning"></i>
                                            <span class="fs-sm text-secondary-emphasis">4.7</span>
                                            <span class="fs-xs text-body-secondary align-self-end">(389)</span>
                                        </div>
                                        <div class="d-flex align-items-center gap-1 min-w-0 fs-sm">
                                            <i class="fi-map-pin"></i>
                                            <span class="text-truncate">0.9 km from center</span>
                                        </div>
                                    </div>
                                    <div class="h6 pt-3 mb-0">$42</div>
                                </div>
                            </article>
                        </div>

                        <!-- Listing -->
                        <div class="col">
                            <article class="card h-100 hover-effect-scale hover-effect-opacity bg-transparent">
                                <div class="card-img-top position-relative bg-body-tertiary overflow-hidden">
                                    <div class="ratio hover-effect-target" style="--fn-aspect-ratio: calc(198 / 304 * 100%)">
                                        <img src="assets/img/listings/city-guide/v1/12.jpg" alt="Image">
                                    </div>
                                    <div class="position-absolute top-0 end-0 z-2 hover-effect-target opacity-0 pt-1 pt-sm-0 pe-1 pe-sm-0 mt-2 mt-sm-3 me-2 me-sm-3">
                                        <button type="button" class="btn btn-sm btn-icon btn-light bg-light border-0 rounded-circle animate-pulse" aria-label="Add to wishlist">
                                            <i class="fi-heart animate-target fs-sm"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body pt-3 pb-2 px-3">
                                    <span class="badge text-body-emphasis bg-secondary-subtle text-decoration-none mb-2">Entertainment</span>
                                    <h3 class="h5 pt-1 mb-2">
                                        <a class="hover-effect-underline stretched-link" href="single-entry-city-guide.html">Live Music Boat Tour</a>
                                    </h3>
                                    <p class="fs-sm mb-0">Listen to professional musicians onboard as they entertain you on your journey.</p>
                                </div>
                                <div class="card-footer bg-transparent border-0 pt-0 pb-3 px-3">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex align-items-center gap-1">
                                            <i class="fi-star-filled text-warning"></i>
                                            <span class="fs-sm text-secondary-emphasis">4.5</span>
                                            <span class="fs-xs text-body-secondary align-self-end">(264)</span>
                                        </div>
                                        <div class="d-flex align-items-center gap-1 min-w-0 fs-sm">
                                            <i class="fi-map-pin"></i>
                                            <span class="text-truncate">2.7 km from center</span>
                                        </div>
                                    </div>
                                    <div class="h6 pt-3 mb-0">$50</div>
                                </div>
                            </article>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <nav class="pt-3 mt-3" aria-label="Listings pagination">
                        <ul class="pagination pagination-lg">
                            <li class="page-item disabled me-auto">
                                <a class="page-link d-flex align-items-center h-100 fs-lg rounded-pill px-2" href="#!" aria-label="Previous page">
                                    <i class="fi-chevron-left mx-1"></i>
                                </a>
                            </li>
                            <li class="page-item active" aria-current="page">
                                <span class="page-link rounded-pill">
                                    <span style="margin: 0 1px">1</span>
                                    <span class="visually-hidden">(current)</span>
                                </span>
                            </li>
                            <li class="page-item">
                                <a class="page-link rounded-pill" href="#!">2</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link rounded-pill" href="#!">3</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link rounded-pill" href="#!">4</a>
                            </li>
                            <li class="page-item">
                                <span class="page-link px-2 pe-none">...</span>
                            </li>
                            <li class="page-item">
                                <a class="page-link rounded-pill" href="#!">10</a>
                            </li>
                            <li class="page-item ms-auto">
                                <a class="page-link d-flex align-items-center h-100 fs-lg rounded-pill px-2" href="#" aria-label="Next page">
                                    <i class="fi-chevron-right mx-1"></i>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </main>



    <!-- Map offcanvas -->
    <div class="offcanvas offcanvas-top h-100" id="map" tabindex="-1">
        <div class="offcanvas-header justify-content-between border-bottom py-3">
            <h5 class="offcanvas-title py-1">View on the map</h5>
            <div class="d-flex align-items-center py-1">
                <span class="fs-xs ms-auto me-2">[ESC]</span>
                <button class="btn-close ms-0" type="button" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
        </div>
        <div class="offcanvas-body position-relative">
            <div class="position-absolute top-0 start-0 w-100 h-100 bg-body-tertiary" data-map="{
                 &quot;tileLayer&quot;: &quot;https://api.maptiler.com/maps/pastel/{z}/{x}/{y}.png?key=rqrCHwDtUZCUA2fCt3vV&quot;,
                 &quot;attribution&quot;: &quot;© Maptiler © OpenStreetMap contributors&quot;,
                 &quot;zoom&quot;: 15,
                 &quot;tileSize&quot;: 512,
                 &quot;zoomOffset&quot;: -1,
                 &quot;templates&quot;: {
                 &quot;popup&quot;: &quot;<div class=\&quot;card hover-effect-scale bg-transparent border-0\&quot; data-bs-theme=\&quot;light\&quot;><div class=\&quot;card-img-top position-relative bg-body-tertiary overflow-hidden\&quot;><div class=\&quot;ratio\&quot; style=\&quot;--fn-aspect-ratio: calc(128 / 280 * 100%)\&quot;></div><img src=_%7b%7bimage%7d%7d/_.html class=\&quot;hover-effect-target position-absolute top-0 start-0 w-100 h-100 object-fit-cover\&quot; alt=\&quot;Image\&quot;></div><div class=\&quot;card-body pt-3 pb-1 px-3\&quot;><span class=\&quot;badge text-body-emphasis bg-secondary-subtle text-decoration-none mb-2\&quot;>{{category}}</span><h3 class=\&quot;fs-lg mb-2\&quot;><a class=\&quot;hover-effect-underline stretched-link text-dark-emphasis\&quot; href=_single-entry-city-guide.html/_.html>{{title}}</a></h3></div><div class=\&quot;card-footer bg-transparent border-0 pt-0 pb-3 px-3\&quot;><div class=\&quot;d-flex align-items-center gap-3\&quot;><div class=\&quot;d-flex align-items-center gap-1\&quot;><i class=\&quot;fi-star-filled text-warning\&quot;></i><span class=\&quot;fs-sm text-secondary-emphasis\&quot;>{{rating}}</span><span class=\&quot;fs-xs text-body-secondary align-self-end\&quot;>({{reviews}})</span></div><div class=\&quot;d-flex align-items-center gap-1 min-w-0 fs-sm\&quot;><i class=\&quot;fi-map-pin\&quot;></i><span class=\&quot;text-truncate\&quot;>{{fromCenter}} km from center</span></div></div><div class=\&quot;h6 pt-3 mb-0\&quot;>${{price}}</div></div></div>&quot;
                 }
                 }" data-map-markers="assets/json/map-city-guide.json"></div>
        </div>
    </div>



{% endblock %}